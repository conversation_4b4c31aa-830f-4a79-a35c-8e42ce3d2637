import React from 'react';
import { Link } from 'react-router-dom';
import { BarChart3, Github, Twitter, Linkedin, Mail, Rss } from 'lucide-react';

export const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#0A1128] border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          
          {/* 品牌和描述 */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <BarChart3 className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">AppReview.Today</span>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              AI-powered app review analysis platform that transforms user feedback into actionable insights for mobile app success.
            </p>
            <div className="flex space-x-2">
              <a
                href="https://github.com/happynocode/app-review-analysis"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Github className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com/appreviewtoday"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="https://linkedin.com/company/appreviewtoday"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* 产品链接 */}
          <div>
            <h3 className="text-white font-semibold mb-4">Product</h3>
            <ul className="space-y-1">
              <li>
                <Link to="/features" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Features
                </Link>
              </li>
              <li>
                <Link to="/pricing" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Pricing
                </Link>
              </li>
              <li>
                <Link to="/demo" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Demo
                </Link>
              </li>
              <li>
                <Link to="/how-it-works" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/use-cases" className="text-gray-400 hover:text-white transition-colors text-sm block py-2 px-1 min-h-[44px] flex items-center">
                  Use Cases
                </Link>
              </li>
              <li>
                <Link to="/integrations" className="text-gray-400 hover:text-white transition-colors text-sm block py-2 px-1 min-h-[44px] flex items-center">
                  Integrations
                </Link>
              </li>
            </ul>
          </div>

          {/* Solutions */}
          <div>
            <h3 className="text-white font-semibold mb-4">Solutions</h3>
            <ul className="space-y-1">
              <li>
                <Link to="/for-product-managers" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  For Product Managers
                </Link>
              </li>
              <li>
                <Link to="/for-marketers" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  For Marketers
                </Link>
              </li>
              <li>
                <Link to="/for-researchers" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  For Researchers
                </Link>
              </li>
            </ul>
          </div>

          {/* Industries */}
          <div>
            <h3 className="text-white font-semibold mb-4">Industries</h3>
            <ul className="space-y-1">
              <li>
                <Link to="/gaming-apps" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Gaming Apps
                </Link>
              </li>
              <li>
                <Link to="/ecommerce-apps" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  E-commerce Apps
                </Link>
              </li>
              <li>
                <Link to="/fintech-apps" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Fintech Apps
                </Link>
              </li>
            </ul>
          </div>

          {/* 支持和资源 */}
          <div>
            <h3 className="text-white font-semibold mb-4">Support & Resources</h3>
            <ul className="space-y-1">
              <li>
                <Link to="/faq" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/help" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Help Center
                </Link>
              </li>
              <li>
                <Link to="/getting-started" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Getting Started
                </Link>
              </li>
              <li>
                <Link to="/api-docs" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  API Documentation
                </Link>
              </li>
              <li>
                <Link to="/troubleshooting" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Troubleshooting
                </Link>
              </li>
              <li>
                <Link to="/blog" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Blog
                </Link>
              </li>
              <li>
                <a
                  href={`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/blog-rss-public`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors text-sm py-1 px-1 min-h-[32px] flex items-center gap-1"
                >
                  <Rss className="w-3 h-3" />
                  RSS Feed
                </a>
              </li>
            </ul>
          </div>

          {/* 公司和法律 */}
          <div>
            <h3 className="text-white font-semibold mb-4">Company</h3>
            <ul className="space-y-1">
              <li>
                <Link to="/about" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Contact
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link to="/security" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  Security
                </Link>
              </li>
              <li>
                <Link to="/gdpr" className="text-gray-400 hover:text-white transition-colors text-sm block py-1 px-1 min-h-[32px] flex items-center">
                  GDPR Compliance
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* 底部版权信息 */}
        <div className="border-t border-white/10 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} AppReview.Today. All rights reserved.
            </p>
            <div className="flex space-x-2 mt-4 md:mt-0">
              <Link to="/sitemap" className="text-gray-400 hover:text-white transition-colors text-sm py-1 px-3 min-h-[32px] flex items-center rounded-lg hover:bg-white/5">
                Sitemap
              </Link>
              <a
                href="/robots.txt"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors text-sm py-1 px-3 min-h-[32px] flex items-center rounded-lg hover:bg-white/5"
              >
                Robots.txt
              </a>
              <a
                href="/llms.txt"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors text-sm py-1 px-3 min-h-[32px] flex items-center rounded-lg hover:bg-white/5"
              >
                AI Info
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
