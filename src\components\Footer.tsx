import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { BarChart3, Github, Twitter, Linkedin, Mail, Rss } from 'lucide-react';

export const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#0A1128] border-t border-white/10">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 py-12">
        <div className="flex flex-col md:flex-row gap-2">
          
          {/* 品牌和描述 */}
          <div className="flex-shrink-0">
            <div className="flex items-center space-x-2 mb-4">
              <BarChart3 className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">AppReview.Today</span>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              AI-powered app review analysis for mobile app success.
            </p>
            <div className="flex space-x-2">
              <a
                href="https://github.com/happynocode/app-review-analysis"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Github className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com/appreviewtoday"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="https://linkedin.com/company/appreviewtoday"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/5 min-w-[44px] min-h-[44px] flex items-center justify-center"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* 产品链接 */}
          <div className="flex-shrink-0">
            <h3 className="text-white font-semibold mb-2">Product</h3>
            <ul>
              <li>
                <Link to="/features" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Features
                </Link>
              </li>
              <li>
                <Link to="/pricing" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Pricing
                </Link>
              </li>
              <li>
                <Link to="/demo" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Demo
                </Link>
              </li>
              <li>
                <Link to="/how-it-works" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/use-cases" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Use Cases
                </Link>
              </li>
              <li>
                <Link to="/integrations" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Integrations
                </Link>
              </li>
            </ul>
          </div>

          {/* Solutions */}
          <div className="flex-shrink-0">
            <h3 className="text-white font-semibold mb-2">Solutions</h3>
            <ul>
              <li>
                <Link to="/for-product-managers" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  For Product Managers
                </Link>
              </li>
              <li>
                <Link to="/for-marketers" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  For Marketers
                </Link>
              </li>
              <li>
                <Link to="/for-researchers" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  For Researchers
                </Link>
              </li>
            </ul>
          </div>

          {/* Industries */}
          <div className="flex-shrink-0">
            <h3 className="text-white font-semibold mb-2">Industries</h3>
            <ul>
              <li>
                <Link to="/gaming-apps" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Gaming Apps
                </Link>
              </li>
              <li>
                <Link to="/ecommerce-apps" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  E-commerce Apps
                </Link>
              </li>
              <li>
                <Link to="/fintech-apps" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Fintech Apps
                </Link>
              </li>
            </ul>
          </div>

          {/* 支持和资源 */}
          <div className="flex-shrink-0">
            <h3 className="text-white font-semibold mb-2">Support & Resources</h3>
            <ul>
              <li>
                <Link to="/faq" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/help" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Help Center
                </Link>
              </li>
              <li>
                <Link to="/getting-started" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Getting Started
                </Link>
              </li>
              <li>
                <Link to="/api-docs" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  API Documentation
                </Link>
              </li>
              <li>
                <Link to="/troubleshooting" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Troubleshooting
                </Link>
              </li>
              <li>
                <Link to="/blog" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Blog
                </Link>
              </li>
              <li>
                <a
                  href={`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/blog-rss-public`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors text-sm py-0 px-1 min-h-[24px] flex items-center gap-1 leading-tight"
                >
                  <Rss className="w-3 h-3" />
                  RSS Feed
                </a>
              </li>
            </ul>
          </div>

          {/* 公司和法律 */}
          <div className="flex-shrink-0 md:w-1/5">
            <h3 className="text-white font-semibold mb-2">Company</h3>
            <ul>
              <li>
                <Link to="/about" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Contact
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link to="/security" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  Security
                </Link>
              </li>
              <li>
                <Link to="/gdpr" className="text-gray-400 hover:text-white transition-colors text-sm block py-0 px-1 min-h-[24px] flex items-center leading-tight">
                  GDPR Compliance
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* 底部版权信息 */}
        <div className="border-t border-white/10 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} AppReview.Today. All rights reserved.
            </p>
            <div className="flex space-x-2 mt-4 md:mt-0">
              <Link to="/sitemap" className="text-gray-400 hover:text-white transition-colors text-sm py-0 px-3 min-h-[24px] flex items-center rounded-lg hover:bg-white/5 leading-tight">
                Sitemap
              </Link>
              <a
                href="/robots.txt"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors text-sm py-0 px-3 min-h-[24px] flex items-center rounded-lg hover:bg-white/5 leading-tight"
              >
                Robots.txt
              </a>
              <a
                href="/llms.txt"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors text-sm py-0 px-3 min-h-[24px] flex items-center rounded-lg hover:bg-white/5 leading-tight"
              >
                AI Info
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
